<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产环境监控仪表板 - Linus重构版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Arial, sans-serif;
            background: #0f1419;
            color: #e6e6e6;
            overflow-x: hidden;
        }

        .dashboard {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: 100vh;
        }

        .sidebar {
            background: #1a1f2e;
            padding: 20px;
            border-right: 1px solid #2d3748;
        }

        .logo {
            font-size: 1.5em;
            font-weight: bold;
            color: #4299e1;
            margin-bottom: 30px;
            text-align: center;
        }

        .nav-item {
            padding: 12px 16px;
            margin-bottom: 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            background: none;
            color: #a0aec0;
            width: 100%;
            text-align: left;
            font-size: 14px;
        }

        .nav-item:hover,
        .nav-item.active {
            background: #2d3748;
            color: #4299e1;
        }

        .main-content {
            padding: 20px;
            overflow-y: auto;
            max-height: 100vh;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #2d3748;
        }

        .header h1 {
            color: #e6e6e6;
            font-size: 1.8em;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-online {
            background: #48bb78;
            color: white;
        }

        .status-warning {
            background: #ed8936;
            color: white;
        }

        .status-error {
            background: #f56565;
            color: white;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: #1a202c;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #2d3748;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #4299e1, #667eea);
        }

        .metric-title {
            font-size: 14px;
            color: #a0aec0;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #e6e6e6;
            margin-bottom: 5px;
        }

        .metric-change {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .metric-change.positive {
            color: #48bb78;
        }

        .metric-change.negative {
            color: #f56565;
        }

        .chart-container {
            background: #1a202c;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #2d3748;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 16px;
            color: #e6e6e6;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .chart {
            height: 200px;
            background: #0f1419;
            border-radius: 6px;
            padding: 15px;
            position: relative;
            overflow: hidden;
        }

        .chart-line {
            position: absolute;
            bottom: 15px;
            left: 15px;
            right: 15px;
            height: 2px;
            background: linear-gradient(90deg, #4299e1, #667eea);
            border-radius: 1px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        .alerts-panel {
            background: #1a202c;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #2d3748;
        }

        .alert-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            margin-bottom: 10px;
            background: #2d3748;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .alert-critical {
            border-left-color: #f56565;
        }

        .alert-warning {
            border-left-color: #ed8936;
        }

        .alert-info {
            border-left-color: #4299e1;
        }

        .alert-icon {
            font-size: 18px;
        }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .alert-time {
            font-size: 12px;
            color: #a0aec0;
        }

        .system-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .info-item {
            background: #2d3748;
            padding: 15px;
            border-radius: 8px;
        }

        .info-label {
            font-size: 12px;
            color: #a0aec0;
            margin-bottom: 5px;
        }

        .info-value {
            font-weight: bold;
            color: #e6e6e6;
        }

        .control-panel {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .control-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .control-btn.primary {
            background: #4299e1;
            color: white;
        }

        .control-btn.secondary {
            background: #2d3748;
            color: #e6e6e6;
            border: 1px solid #4a5568;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .logs-viewer {
            background: #0f1419;
            border-radius: 8px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            border: 1px solid #2d3748;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-timestamp {
            color: #a0aec0;
        }

        .log-level-info {
            color: #4299e1;
        }

        .log-level-warning {
            color: #ed8936;
        }

        .log-level-error {
            color: #f56565;
        }

        .hidden {
            display: none !important;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #a0aec0;
        }

        .loading::after {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #4299e1;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                display: none;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="sidebar">
            <div class="logo">🚀 OTA Monitor</div>
            <nav>
                <button class="nav-item active" onclick="showPanel('overview')">📊 概览</button>
                <button class="nav-item" onclick="showPanel('performance')">⚡ 性能</button>
                <button class="nav-item" onclick="showPanel('errors')">🔥 错误</button>
                <button class="nav-item" onclick="showPanel('alerts')">🚨 告警</button>
                <button class="nav-item" onclick="showPanel('logs')">📋 日志</button>
                <button class="nav-item" onclick="showPanel('system')">🔧 系统</button>
                <button class="nav-item" onclick="runLoadTest()">🧪 负载测试</button>
            </nav>
        </div>

        <div class="main-content">
            <div class="header">
                <h1 id="panel-title">系统概览</h1>
                <div class="status-badge status-online" id="system-status">系统正常</div>
            </div>

            <div class="control-panel">
                <button class="control-btn primary" onclick="refreshData()">🔄 刷新数据</button>
                <button class="control-btn secondary" onclick="exportData()">📥 导出报告</button>
                <button class="control-btn secondary" onclick="toggleAutoRefresh()">⏱️ 自动刷新</button>
            </div>

            <!-- 概览面板 -->
            <div id="overview-panel" class="panel">
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-title">响应时间</div>
                        <div class="metric-value" id="response-time">--</div>
                        <div class="metric-change positive" id="response-time-change">
                            ↗ +15% 比昨天
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-title">吞吐量</div>
                        <div class="metric-value" id="throughput">--</div>
                        <div class="metric-change positive" id="throughput-change">
                            ↗ +23% 比昨天
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-title">错误率</div>
                        <div class="metric-value" id="error-rate">--</div>
                        <div class="metric-change negative" id="error-rate-change">
                            ↘ -12% 比昨天
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-title">活跃用户</div>
                        <div class="metric-value" id="active-users">--</div>
                        <div class="metric-change positive" id="active-users-change">
                            ↗ +8% 比昨天
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">实时性能趋势</div>
                    <div class="chart">
                        <div class="chart-line"></div>
                    </div>
                </div>
            </div>

            <!-- 性能面板 -->
            <div id="performance-panel" class="panel hidden">
                <div class="chart-container">
                    <div class="chart-title">CPU 使用率</div>
                    <div class="chart" id="cpu-chart">
                        <div class="loading">加载性能数据</div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">内存使用情况</div>
                    <div class="chart" id="memory-chart">
                        <div class="loading">加载内存数据</div>
                    </div>
                </div>

                <div class="system-info">
                    <div class="info-item">
                        <div class="info-label">加载时间</div>
                        <div class="info-value" id="load-time">--</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">API响应</div>
                        <div class="info-value" id="api-response">--</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">缓存命中率</div>
                        <div class="info-value" id="cache-hit-rate">--</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">数据库连接</div>
                        <div class="info-value" id="db-connections">--</div>
                    </div>
                </div>
            </div>

            <!-- 错误面板 -->
            <div id="errors-panel" class="panel hidden">
                <div class="chart-container">
                    <div class="chart-title">错误趋势</div>
                    <div class="chart">
                        <div id="error-chart-data"></div>
                    </div>
                </div>

                <div class="alerts-panel">
                    <h3 style="margin-bottom: 20px; color: #e6e6e6;">最近错误</h3>
                    <div id="recent-errors">
                        <div class="loading">加载错误数据</div>
                    </div>
                </div>
            </div>

            <!-- 告警面板 -->
            <div id="alerts-panel" class="panel hidden">
                <div class="alerts-panel">
                    <h3 style="margin-bottom: 20px; color: #e6e6e6;">活跃告警</h3>
                    <div id="active-alerts">
                        <div class="alert-item alert-warning">
                            <div class="alert-icon">⚠️</div>
                            <div class="alert-content">
                                <div class="alert-title">API响应时间过长</div>
                                <div class="alert-time">2分钟前</div>
                            </div>
                        </div>
                        
                        <div class="alert-item alert-info">
                            <div class="alert-icon">ℹ️</div>
                            <div class="alert-content">
                                <div class="alert-title">缓存刷新完成</div>
                                <div class="alert-time">5分钟前</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 日志面板 -->
            <div id="logs-panel" class="panel hidden">
                <div class="chart-container">
                    <div class="chart-title">系统日志</div>
                    <div class="logs-viewer" id="logs-viewer">
                        <div class="loading">加载日志数据</div>
                    </div>
                </div>
            </div>

            <!-- 系统面板 -->
            <div id="system-panel" class="panel hidden">
                <div class="system-info">
                    <div class="info-item">
                        <div class="info-label">系统版本</div>
                        <div class="info-value" id="system-version">1.0.0-linus-refactor</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">构建时间</div>
                        <div class="info-value" id="build-time">--</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">运行时间</div>
                        <div class="info-value" id="uptime">--</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">部署环境</div>
                        <div class="info-value" id="environment">--</div>
                    </div>
                </div>

                <div class="chart-container" style="margin-top: 20px;">
                    <div class="chart-title">系统资源</div>
                    <div id="system-resources">
                        <div class="loading">加载系统信息</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 核心脚本 -->
    <script src="js/core.js"></script>
    <script src="js/performance-monitor.js"></script>
    <script src="js/error-monitor.js"></script>
    <script src="load-test.js"></script>

    <script>
        // 监控仪表板控制器
        class MonitoringDashboard {
            constructor() {
                this.autoRefresh = true;
                this.refreshInterval = 30000; // 30秒
                this.currentPanel = 'overview';
                this.refreshTimer = null;
                
                this.init();
            }

            init() {
                this.startAutoRefresh();
                this.loadInitialData();
                
                // 每5秒更新实时指标
                setInterval(() => this.updateRealTimeMetrics(), 5000);
            }

            startAutoRefresh() {
                if (this.refreshTimer) {
                    clearInterval(this.refreshTimer);
                }
                
                if (this.autoRefresh) {
                    this.refreshTimer = setInterval(() => {
                        this.refreshData();
                    }, this.refreshInterval);
                }
            }

            loadInitialData() {
                this.updateOverviewMetrics();
                this.updateSystemInfo();
                this.loadRecentErrors();
                this.loadSystemLogs();
            }

            updateRealTimeMetrics() {
                // 模拟实时数据更新
                const responseTime = Math.floor(Math.random() * 200) + 50;
                const throughput = Math.floor(Math.random() * 100) + 200;
                const errorRate = (Math.random() * 0.05).toFixed(3);
                const activeUsers = Math.floor(Math.random() * 50) + 100;

                document.getElementById('response-time').textContent = responseTime + 'ms';
                document.getElementById('throughput').textContent = throughput + ' req/s';
                document.getElementById('error-rate').textContent = errorRate + '%';
                document.getElementById('active-users').textContent = activeUsers;

                // 更新系统状态
                this.updateSystemStatus(responseTime, parseFloat(errorRate));
            }

            updateSystemStatus(responseTime, errorRate) {
                const statusElement = document.getElementById('system-status');
                
                if (responseTime > 1000 || errorRate > 0.05) {
                    statusElement.className = 'status-badge status-error';
                    statusElement.textContent = '系统异常';
                } else if (responseTime > 500 || errorRate > 0.02) {
                    statusElement.className = 'status-badge status-warning';
                    statusElement.textContent = '性能警告';
                } else {
                    statusElement.className = 'status-badge status-online';
                    statusElement.textContent = '系统正常';
                }
            }

            updateOverviewMetrics() {
                // 从性能监控器获取数据
                if (window.performanceMonitor) {
                    const metrics = window.performanceMonitor.getMetrics();
                    console.log('性能指标:', metrics);
                }

                // 从错误监控器获取数据
                if (window.errorMonitor) {
                    const errors = window.errorMonitor.getErrorSummary();
                    console.log('错误摘要:', errors);
                }
            }

            updateSystemInfo() {
                if (window.productionConfig) {
                    const info = window.productionConfig.getEnvironmentInfo();
                    document.getElementById('system-version').textContent = info.version;
                    document.getElementById('environment').textContent = info.environment;
                    document.getElementById('build-time').textContent = new Date(info.build).toLocaleString();
                }

                // 计算运行时间
                const startTime = performance.timing.navigationStart;
                const uptime = Date.now() - startTime;
                document.getElementById('uptime').textContent = this.formatUptime(uptime);
            }

            loadRecentErrors() {
                const errorsContainer = document.getElementById('recent-errors');
                
                if (window.errorMonitor) {
                    const errors = window.errorMonitor.getErrors().slice(-5);
                    
                    if (errors.length === 0) {
                        errorsContainer.innerHTML = '<div style="text-align: center; color: #48bb78;">✅ 暂无错误</div>';
                        return;
                    }

                    errorsContainer.innerHTML = errors.map(error => `
                        <div class="alert-item alert-${this.getErrorSeverity(error)}">
                            <div class="alert-icon">${this.getErrorIcon(error)}</div>
                            <div class="alert-content">
                                <div class="alert-title">${error.type}: ${error.message}</div>
                                <div class="alert-time">${this.formatTime(error.timestamp)}</div>
                            </div>
                        </div>
                    `).join('');
                } else {
                    errorsContainer.innerHTML = '<div style="text-align: center; color: #a0aec0;">错误监控未启用</div>';
                }
            }

            loadSystemLogs() {
                const logsViewer = document.getElementById('logs-viewer');
                
                // 模拟日志数据
                const logs = [
                    { level: 'info', message: 'OTA系统启动完成', timestamp: Date.now() - 60000 },
                    { level: 'info', message: '性能监控已启动', timestamp: Date.now() - 55000 },
                    { level: 'info', message: '用户登录: <EMAIL>', timestamp: Date.now() - 30000 },
                    { level: 'warning', message: 'API响应时间较慢: 1.2s', timestamp: Date.now() - 15000 },
                    { level: 'info', message: '订单创建成功: #12345', timestamp: Date.now() - 5000 }
                ];

                logsViewer.innerHTML = logs.map(log => `
                    <div class="log-entry">
                        <span class="log-timestamp">[${this.formatLogTime(log.timestamp)}]</span>
                        <span class="log-level-${log.level}">[${log.level.toUpperCase()}]</span>
                        ${log.message}
                    </div>
                `).join('');

                // 滚动到底部
                logsViewer.scrollTop = logsViewer.scrollHeight;
            }

            refreshData() {
                console.log('🔄 刷新监控数据...');
                this.loadInitialData();
                
                // 显示刷新提示
                this.showNotification('数据已刷新', 'success');
            }

            exportData() {
                const data = {
                    timestamp: new Date().toISOString(),
                    metrics: window.performanceMonitor ? window.performanceMonitor.getMetrics() : [],
                    errors: window.errorMonitor ? window.errorMonitor.getErrors() : [],
                    system: window.productionConfig ? window.productionConfig.getEnvironmentInfo() : {}
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `monitoring-report-${Date.now()}.json`;
                a.click();
                URL.revokeObjectURL(url);

                this.showNotification('报告已导出', 'success');
            }

            toggleAutoRefresh() {
                this.autoRefresh = !this.autoRefresh;
                this.startAutoRefresh();
                
                const message = this.autoRefresh ? '自动刷新已开启' : '自动刷新已关闭';
                this.showNotification(message, 'info');
            }

            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#48bb78' : type === 'error' ? '#f56565' : '#4299e1'};
                    color: white;
                    padding: 12px 20px;
                    border-radius: 6px;
                    z-index: 10000;
                    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
                `;
                notification.textContent = message;
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }

            // 工具方法
            formatUptime(ms) {
                const seconds = Math.floor(ms / 1000) % 60;
                const minutes = Math.floor(ms / (1000 * 60)) % 60;
                const hours = Math.floor(ms / (1000 * 60 * 60)) % 24;
                const days = Math.floor(ms / (1000 * 60 * 60 * 24));

                if (days > 0) return `${days}天 ${hours}小时`;
                if (hours > 0) return `${hours}小时 ${minutes}分钟`;
                return `${minutes}分钟 ${seconds}秒`;
            }

            formatTime(timestamp) {
                const now = Date.now();
                const diff = now - timestamp;
                
                if (diff < 60000) return '刚刚';
                if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
                if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
                return new Date(timestamp).toLocaleString();
            }

            formatLogTime(timestamp) {
                return new Date(timestamp).toLocaleTimeString();
            }

            getErrorSeverity(error) {
                if (error.severity >= 4) return 'critical';
                if (error.severity >= 3) return 'warning';
                return 'info';
            }

            getErrorIcon(error) {
                if (error.severity >= 4) return '🔥';
                if (error.severity >= 3) return '⚠️';
                return 'ℹ️';
            }
        }

        // 面板切换
        function showPanel(panelName) {
            // 隐藏所有面板
            document.querySelectorAll('.panel').forEach(panel => {
                panel.classList.add('hidden');
            });

            // 显示选中面板
            document.getElementById(panelName + '-panel').classList.remove('hidden');

            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新标题
            const titles = {
                overview: '系统概览',
                performance: '性能监控',
                errors: '错误监控',
                alerts: '告警管理',
                logs: '系统日志',
                system: '系统信息'
            };
            document.getElementById('panel-title').textContent = titles[panelName];

            dashboard.currentPanel = panelName;
        }

        // 负载测试
        async function runLoadTest() {
            if (!window.runLoadTest) {
                dashboard.showNotification('负载测试工具未加载', 'error');
                return;
            }

            dashboard.showNotification('开始负载测试...', 'info');
            
            try {
                const result = await window.runLoadTest('light');
                console.log('负载测试结果:', result);
                dashboard.showNotification('负载测试完成', 'success');
            } catch (error) {
                console.error('负载测试失败:', error);
                dashboard.showNotification('负载测试失败', 'error');
            }
        }

        // 全局函数
        function refreshData() {
            dashboard.refreshData();
        }

        function exportData() {
            dashboard.exportData();
        }

        function toggleAutoRefresh() {
            dashboard.toggleAutoRefresh();
        }

        // 初始化仪表板
        const dashboard = new MonitoringDashboard();

        console.log('🚀 监控仪表板已启动 - Linus重构版');
    </script>
</body>
</html>