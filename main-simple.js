/**
 * 简化入口文件 - <PERSON><PERSON> Torvalds式重构版本
 * 
 * 从300行的复杂启动逻辑简化为50行直接代码
 * 消除ApplicationBootstrap等企业级废话
 */

'use strict';

// 简化的应用入口
class SimpleOTAApp {
    constructor() {
        this.startTime = performance.now();
        this.isInitialized = false;
    }
    
    async start() {
        console.log('🚀 OTA订单处理系统启动中...');
        
        try {
            // 检查依赖
            this.checkDependencies();
            
            // 🚀 修复：初始化主题和语言设置
            this.initTheme();
            this.initLanguage();
            
            // 初始化认证
            this.initAuth();
            
            // 设置全局错误处理
            this.setupErrorHandling();
            
            // 绑定全局事件
            this.bindGlobalEvents();
            
            // 标记初始化完成
            this.isInitialized = true;
            
            const totalTime = performance.now() - this.startTime;
            console.log(`✅ OTA系统启动完成，总耗时: ${totalTime.toFixed(1)}ms`);
            
            // 显示启动成功信息
            this.showStartupSuccess(totalTime);
            
        } catch (error) {
            console.error('❌ OTA系统启动失败:', error);
            this.showStartupError(error);
        }
    }
    
    checkDependencies() {
        if (!window.ota) {
            throw new Error('核心模块未加载');
        }
        
        if (!window.ota.api || !window.ota.gemini || !window.ota.ui) {
            throw new Error('关键模块缺失');
        }
        
        console.log('✅ 依赖检查通过');
    }
    
    initAuth() {
        // 检查登录状态
        const token = localStorage.getItem('access_token');
        const userEmail = localStorage.getItem('user_email');

        if (token && userEmail) {
            console.log(`✅ 用户已登录: ${userEmail}`);
            // Linus式解决方案：初始化时设置正确的状态类
            document.body.classList.add('logged-in');
            this.showMainWorkspace();
        } else {
            console.log('🔑 用户未登录，显示登录界面');
            // Linus式解决方案：确保移除状态类
            document.body.classList.remove('logged-in');
            this.showLoginPanel();
        }
    }
    
    setupErrorHandling() {
        // 全局错误处理
        window.addEventListener('error', (event) => {
            console.error('全局错误:', event.error);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise拒绝:', event.reason);
        });
        
        console.log('✅ 全局错误处理已设置');
    }
    
    bindGlobalEvents() {
        // 登录表单
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }
        
        // 退出登录
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.handleLogout());
        }
        
        // 历史订单
        const historyBtn = document.getElementById('historyBtn');
        if (historyBtn) {
            historyBtn.addEventListener('click', () => this.showOrderHistory());
        }
        
        // 🚀 修复：主题切换
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }
        
        // 🚀 修复：语言切换
        const languageSelect = document.getElementById('languageSelect');
        if (languageSelect) {
            languageSelect.addEventListener('change', (e) => this.changeLanguage(e.target.value));
        }
        
        console.log('✅ 全局事件已绑定');
    }
    
    async handleLogin(event) {
        event.preventDefault();
        
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        
        if (!email || !password) {
            alert('请输入邮箱和密码');
            return;
        }
        
        try {
            console.log('🔑 正在登录...');
            
            const response = await fetch('https://gomyhire.com.my/api/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email, password })
            });
            
            if (!response.ok) {
                throw new Error('登录失败');
            }
            
            const data = await response.json();
            
            // 保存登录信息
            localStorage.setItem('access_token', data.access_token);
            localStorage.setItem('user_email', email);
            
            console.log('✅ 登录成功');
            this.showMainWorkspace();
            
        } catch (error) {
            console.error('❌ 登录失败:', error);
            alert('登录失败，请检查邮箱和密码');
        }
    }
    
    handleLogout() {
        localStorage.removeItem('access_token');
        localStorage.removeItem('user_email');
        
        console.log('✅ 已退出登录');
        this.showLoginPanel();
    }
    
    showLoginPanel() {
        const loginPanel = document.getElementById('loginPanel');
        const workspace = document.getElementById('workspace');

        // Linus式解决方案：移除状态类，让CSS处理一切
        document.body.classList.remove('logged-in');

        if (loginPanel) {
            loginPanel.classList.remove('hidden');
            loginPanel.removeAttribute('aria-hidden');
            loginPanel.removeAttribute('inert');
        }
        if (workspace) {
            workspace.classList.add('hidden');
            // 🚀 修复：使用inert替代aria-hidden，避免焦点冲突
            workspace.setAttribute('inert', '');
        }
    }
    
    showMainWorkspace() {
        const loginPanel = document.getElementById('loginPanel');
        const workspace = document.getElementById('workspace');

        // Linus式解决方案：添加状态类，让CSS处理一切
        document.body.classList.add('logged-in');

        if (loginPanel) {
            loginPanel.classList.add('hidden');
            // 🚀 修复：使用inert替代aria-hidden，避免焦点冲突
            loginPanel.setAttribute('inert', '');
        }
        if (workspace) {
            workspace.classList.remove('hidden');
            workspace.removeAttribute('aria-hidden');
            workspace.removeAttribute('inert');
        }

        // 显示用户信息
        this.updateUserInfo();
    }
    
    updateUserInfo() {
        const userEmail = localStorage.getItem('user_email');
        const currentUserElement = document.getElementById('currentUser');
        const userInfoElement = document.getElementById('userInfo');
        
        if (currentUserElement && userEmail) {
            currentUserElement.textContent = userEmail;
        }
        
        if (userInfoElement) {
            userInfoElement.classList.remove('hidden');
            // 🚀 修复：移除aria-hidden，避免可访问性问题
            userInfoElement.removeAttribute('aria-hidden');
        }
    }
    
    async showOrderHistory() {
        try {
            console.log('📋 获取订单历史...');
            
            const orders = await window.ota.api.getOrderHistory({ limit: 50 });
            
            // 这里可以显示历史订单界面
            console.log(`✅ 获取到 ${orders.length} 条订单记录`);
            
        } catch (error) {
            console.error('❌ 获取订单历史失败:', error);
            alert('获取订单历史失败');
        }
    }
    
    showStartupSuccess(totalTime) {
        // 在状态栏显示成功信息
        const connectionStatus = document.getElementById('connectionStatus');
        const dataStatus = document.getElementById('dataStatus');
        
        if (connectionStatus) {
            connectionStatus.textContent = '🟢 已连接';
            connectionStatus.className = 'status-item status-connected';
        }
        
        if (dataStatus) {
            dataStatus.textContent = '📊 数据已加载';
            dataStatus.className = 'status-item status-ready';
        }
        
        // 显示启动时间
        const lastUpdate = document.getElementById('lastUpdate');
        if (lastUpdate) {
            lastUpdate.textContent = `⚡ 启动耗时: ${totalTime.toFixed(0)}ms`;
        }
    }
    
    showStartupError(error) {
        // 显示错误状态
        const connectionStatus = document.getElementById('connectionStatus');
        if (connectionStatus) {
            connectionStatus.textContent = '🔴 启动失败';
            connectionStatus.className = 'status-item status-error';
        }
        
        alert(`系统启动失败: ${error.message}`);
    }
    
    // 🚀 新增：主题切换功能
    toggleTheme() {
        const body = document.body;
        const themeToggle = document.getElementById('themeToggle');
        
        if (body.classList.contains('dark-mode')) {
            body.classList.remove('dark-mode');
            if (themeToggle) themeToggle.textContent = '🌙';
            localStorage.setItem('theme', 'light');
            console.log('✅ 切换到浅色主题');
        } else {
            body.classList.add('dark-mode');
            if (themeToggle) themeToggle.textContent = '☀️';
            localStorage.setItem('theme', 'dark');
            console.log('✅ 切换到深色主题');
        }
    }
    
    // 🚀 新增：语言切换功能
    changeLanguage(language) {
        console.log(`🌐 切换语言到: ${language}`);
        localStorage.setItem('language', language);
        
        // 这里可以实现国际化逻辑
        // 暂时只是保存设置和显示提示
        const message = language === 'zh' ? '已切换到中文' : 'Switched to English';
        
        // 显示切换提示
        this.showLanguageNotification(message);
    }
    
    // 🚀 新增：显示语言切换通知
    showLanguageNotification(message) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4299e1;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 10000;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        `;
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
    
    // 🚀 新增：初始化主题设置
    initTheme() {
        const savedTheme = localStorage.getItem('theme');
        const themeToggle = document.getElementById('themeToggle');
        
        if (savedTheme === 'dark') {
            document.body.classList.add('dark-mode');
            if (themeToggle) themeToggle.textContent = '☀️';
        } else {
            if (themeToggle) themeToggle.textContent = '🌙';
        }
    }
    
    // 🚀 新增：初始化语言设置
    initLanguage() {
        const savedLanguage = localStorage.getItem('language') || 'zh';
        const languageSelect = document.getElementById('languageSelect');
        
        if (languageSelect) {
            languageSelect.value = savedLanguage;
        }
    }
}

// 自动启动应用
const app = new SimpleOTAApp();

// DOM加载完成后启动
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => app.start());
} else {
    app.start();
}

// 暴露到全局供调试使用
window.otaApp = app;

console.log('✅ 简化入口文件已加载');