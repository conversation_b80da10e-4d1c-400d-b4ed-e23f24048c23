<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览器兼容性测试 - OTA系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-header {
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .browser-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 OTA系统浏览器兼容性测试</h1>
            <p>测试系统在不同浏览器中的兼容性，特别是 Promise 错误处理和服务注册机制</p>
        </div>

        <div class="browser-info" id="browserInfo">
            <h3>🌐 浏览器信息</h3>
            <div id="browserDetails">检测中...</div>
        </div>

        <div class="test-container">
            <h3>🧪 兼容性测试</h3>
            <button onclick="runAllTests()">运行所有测试</button>
            <button onclick="testServiceLocator()">测试服务定位器</button>
            <button onclick="testIntegrityChecker()">测试完整性检查器</button>
            <button onclick="testPromiseErrorHandling()">测试Promise错误处理</button>
            <button onclick="clearResults()">清除结果</button>
            
            <div id="testResults"></div>
        </div>

        <div class="test-container">
            <h3>📋 详细日志</h3>
            <div class="log-container" id="logContainer"></div>
        </div>
    </div>

    <!-- 加载核心脚本 -->
    <script src="js/core/script-manifest.js"></script>
    <script src="js/core/script-loader.js"></script>

    <script>
        let testResults = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `test-result ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function addTestResult(testName, status, details) {
            const result = { testName, status, details, timestamp: new Date() };
            testResults.push(result);
            
            const resultsContainer = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${status}`;
            resultDiv.innerHTML = `
                <strong>${testName}</strong>: ${status.toUpperCase()}<br>
                <small>${details}</small>
            `;
            resultsContainer.appendChild(resultDiv);
        }

        function clearResults() {
            testResults = [];
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('logContainer').innerHTML = '';
        }

        // 浏览器检测
        function detectBrowser() {
            const userAgent = navigator.userAgent;
            const isChrome = /Chrome/.test(userAgent) && !/Edg/.test(userAgent);
            const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
            const isEdge = /Edg/.test(userAgent);
            const isFirefox = /Firefox/.test(userAgent);
            
            const browserInfo = {
                isChrome,
                isSafari,
                isEdge,
                isFirefox,
                userAgent,
                isStrictErrorHandling: isSafari || isEdge
            };

            const browserDetails = document.getElementById('browserDetails');
            browserDetails.innerHTML = `
                <strong>用户代理:</strong> ${userAgent}<br>
                <strong>浏览器类型:</strong> ${
                    isChrome ? 'Chrome' : 
                    isSafari ? 'Safari' : 
                    isEdge ? 'Edge' : 
                    isFirefox ? 'Firefox' : 
                    '未知'
                }<br>
                <strong>严格错误处理:</strong> ${browserInfo.isStrictErrorHandling ? '是' : '否'}
            `;

            return browserInfo;
        }

        // 测试服务定位器
        async function testServiceLocator() {
            log('开始测试服务定位器...', 'info');
            
            try {
                // 等待脚本加载完成
                await waitForScripts();
                
                if (!window.OTA || !window.OTA.serviceLocator) {
                    throw new Error('服务定位器未加载');
                }

                const serviceLocator = window.OTA.serviceLocator;
                
                // 测试获取 multiOrderManagerAdapter 服务
                try {
                    const service = serviceLocator.getService('multiOrderManagerAdapter');
                    if (service) {
                        addTestResult('服务定位器 - multiOrderManagerAdapter', 'success', '服务获取成功');
                        log('✅ multiOrderManagerAdapter 服务获取成功', 'success');
                    } else {
                        addTestResult('服务定位器 - multiOrderManagerAdapter', 'warning', '服务为空');
                        log('⚠️ multiOrderManagerAdapter 服务为空', 'warning');
                    }
                } catch (error) {
                    addTestResult('服务定位器 - multiOrderManagerAdapter', 'error', error.message);
                    log(`❌ multiOrderManagerAdapter 服务获取失败: ${error.message}`, 'error');
                }

            } catch (error) {
                addTestResult('服务定位器测试', 'error', error.message);
                log(`❌ 服务定位器测试失败: ${error.message}`, 'error');
            }
        }

        // 测试完整性检查器
        async function testIntegrityChecker() {
            log('开始测试完整性检查器...', 'info');
            
            try {
                await waitForScripts();
                
                if (!window.OTA || !window.OTA.systemIntegrityChecker) {
                    throw new Error('完整性检查器未加载');
                }

                const checker = window.OTA.systemIntegrityChecker;
                
                // 运行完整性检查
                const result = await checker.runIntegrityCheck();
                
                if (result && result.summary) {
                    addTestResult('完整性检查器', 'success', `检查完成，得分: ${result.summary.score || 'N/A'}`);
                    log(`✅ 完整性检查完成，得分: ${result.summary.score || 'N/A'}`, 'success');
                } else {
                    addTestResult('完整性检查器', 'warning', '检查完成但结果异常');
                    log('⚠️ 完整性检查完成但结果异常', 'warning');
                }

            } catch (error) {
                addTestResult('完整性检查器测试', 'error', error.message);
                log(`❌ 完整性检查器测试失败: ${error.message}`, 'error');
            }
        }

        // 测试Promise错误处理
        async function testPromiseErrorHandling() {
            log('开始测试Promise错误处理...', 'info');
            
            const browserInfo = detectBrowser();
            
            // 测试未捕获的Promise错误
            const testPromise = new Promise((resolve, reject) => {
                setTimeout(() => {
                    reject(new Error('测试用的Promise错误'));
                }, 100);
            });

            // 不捕获这个错误，看浏览器如何处理
            testPromise.catch(() => {
                // 实际上我们会捕获它，避免真正的错误
                addTestResult('Promise错误处理', 'success', '错误被正确捕获');
                log('✅ Promise错误被正确捕获', 'success');
            });

            // 测试全局错误处理器是否工作
            setTimeout(() => {
                if (browserInfo.isStrictErrorHandling) {
                    log('📝 当前浏览器使用严格的错误处理机制', 'info');
                } else {
                    log('📝 当前浏览器使用宽松的错误处理机制', 'info');
                }
            }, 200);
        }

        // 等待脚本加载完成
        function waitForScripts() {
            return new Promise((resolve) => {
                const checkInterval = setInterval(() => {
                    if (window.OTA && window.OTA.serviceLocator && window.OTA.systemIntegrityChecker) {
                        clearInterval(checkInterval);
                        resolve();
                    }
                }, 100);
                
                // 最多等待10秒
                setTimeout(() => {
                    clearInterval(checkInterval);
                    resolve();
                }, 10000);
            });
        }

        // 运行所有测试
        async function runAllTests() {
            clearResults();
            log('🚀 开始运行所有兼容性测试...', 'info');
            
            const browserInfo = detectBrowser();
            log(`🌐 检测到浏览器: ${browserInfo.userAgent}`, 'info');
            
            await testServiceLocator();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testIntegrityChecker();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testPromiseErrorHandling();
            
            log('🏁 所有测试完成', 'success');
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            detectBrowser();
            log('📋 浏览器兼容性测试页面已加载', 'info');
            
            // 设置全局错误处理器
            window.addEventListener('error', (event) => {
                log(`全局错误: ${event.error?.message || event.message}`, 'error');
            });
            
            window.addEventListener('unhandledrejection', (event) => {
                log(`未处理的Promise拒绝: ${event.reason}`, 'error');
            });
        });
    </script>
</body>
</html>
