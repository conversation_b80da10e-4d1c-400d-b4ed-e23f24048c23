<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单系统V2测试页面</title>
    
    <!-- 引入样式 -->
    <link rel="stylesheet" href="js/pages/multi-order/styles/multi-order-page.css">
    <link rel="stylesheet" href="js/pages/multi-order/styles/components.css">
    <link rel="stylesheet" href="js/pages/multi-order/styles/responsive.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .test-btn.primary {
            background: #007bff;
            color: white;
        }
        
        .test-btn.primary:hover {
            background: #0056b3;
        }
        
        .test-btn.secondary {
            background: #6c757d;
            color: white;
        }
        
        .test-btn.secondary:hover {
            background: #545b62;
        }
        
        .test-btn.success {
            background: #28a745;
            color: white;
        }
        
        .test-btn.success:hover {
            background: #1e7e34;
        }
        
        .test-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .hidden {
            display: none !important;
        }
        
        /* 多订单页面样式 */
        .page-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .multi-order-panel {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 90vw;
            max-height: 90vh;
            overflow-y: auto;
            padding: 20px;
        }
        
        .multi-order-header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 20px;
            opacity: 0.5;
            transition: opacity 0.3s ease;
        }
        
        .step.active {
            opacity: 1;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .step.active .step-number {
            background: #007bff;
            color: white;
        }
        
        .step-content {
            min-height: 400px;
        }
        
        .step-panel {
            display: none;
        }
        
        .step-panel.active {
            display: block;
        }
        
        .orders-list {
            margin-bottom: 20px;
        }
        
        .multi-order-footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 多订单系统V2测试页面</h1>
            <p>测试新的独立多订单页面架构和组件系统</p>
        </div>
        
        <div class="test-controls">
            <button class="test-btn primary" onclick="testInitialization()">
                🚀 测试初始化
            </button>
            <button class="test-btn success" onclick="testWithSampleOrders()">
                📋 测试样本订单
            </button>
            <button class="test-btn secondary" onclick="testServices()">
                🔧 测试服务
            </button>
            <button class="test-btn secondary" onclick="testComponents()">
                🎨 测试组件
            </button>
            <button class="test-btn warning" onclick="testEndToEnd()">
                🚀 端到端测试
            </button>
            <button class="test-btn secondary" onclick="clearOutput()">
                🧹 清空输出
            </button>
        </div>
        
        <div class="test-output" id="testOutput">
            <div>📝 测试输出将在这里显示...</div>
        </div>
    </div>

    <!-- 引入核心脚本 -->
    <script src="js/core/logger.js"></script>
    <script src="js/core/script-manifest.js"></script>
    
    <script>
        // 测试日志输出
        function logTest(message, level = 'info') {
            const output = document.getElementById('testOutput');
            const timestamp = new Date().toLocaleTimeString();
            const levelIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'warning': '⚠️',
                'error': '❌'
            };
            
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${levelIcon[level] || 'ℹ️'} ${message}`;
            logEntry.style.color = level === 'error' ? '#dc3545' : level === 'success' ? '#28a745' : level === 'warning' ? '#ffc107' : '#333';
            
            output.appendChild(logEntry);
            output.scrollTop = output.scrollHeight;
        }

        // 端到端测试
        async function testEndToEnd() {
            logTest('🚀 开始端到端测试...', 'info');

            try {
                // 等待脚本加载完成
                await waitForScriptsLoaded();

                logTest('📝 模拟多订单检测流程...', 'info');

                // 模拟多订单数据
                const mockMultiOrderData = {
                    orders: [
                        { name: '张三', phone: '13800138001', address: '北京市朝阳区建国路1号' },
                        { name: '李四', phone: '13800138002', address: '上海市浦东新区陆家嘴1号' },
                        { name: '王五', phone: '13800138003', address: '广州市天河区珠江新城1号' }
                    ],
                    originalText: '多订单测试文本',
                    confidence: 95,
                    source: 'end-to-end-test'
                };

                logTest('🎬 测试多订单页面V2显示...', 'info');

                const multiOrderPage = window.OTA?.Pages?.multiOrderPageV2;
                if (multiOrderPage) {
                    await multiOrderPage.show(mockMultiOrderData);
                    logTest('✅ 多订单页面V2显示成功', 'success');
                    logTest(`📊 显示了 ${mockMultiOrderData.orders.length} 个订单`, 'info');

                    // 测试返回主页功能
                    setTimeout(() => {
                        logTest('🏠 测试返回主页功能...', 'info');
                        multiOrderPage.handleBackToMain();
                        logTest('✅ 返回主页功能测试完成', 'success');
                        logTest('🎉 端到端测试流程完成！', 'success');
                        logTest('📝 测试覆盖：显示 → 处理 → 返回', 'info');
                    }, 2000);
                } else {
                    throw new Error('多订单页面V2未找到');
                }

            } catch (error) {
                logTest(`❌ 端到端测试失败: ${error.message}`, 'error');
                console.error('端到端测试错误:', error);
            }
        }

        // 清空输出
        function clearOutput() {
            document.getElementById('testOutput').innerHTML = '<div>📝 测试输出已清空...</div>';
        }

        // 测试初始化
        async function testInitialization() {
            logTest('🚀 开始测试系统初始化...', 'info');
            
            try {
                // 等待脚本加载完成
                await waitForScriptsLoaded();
                
                // 检查命名空间
                if (window.OTA) {
                    logTest('✅ OTA命名空间已加载', 'success');
                } else {
                    logTest('❌ OTA命名空间未找到', 'error');
                    return;
                }
                
                // 检查服务
                const services = ['stateManager', 'orderDetector', 'orderProcessor', 'batchManager', 'apiClient'];
                services.forEach(service => {
                    if (window.OTA.Services?.[service]) {
                        logTest(`✅ 服务已加载: ${service}`, 'success');
                    } else {
                        logTest(`❌ 服务未找到: ${service}`, 'error');
                    }
                });
                
                // 检查组件
                const components = ['BaseComponent', 'OrderCard', 'BatchControls', 'ProgressIndicator', 'StatusPanel'];
                components.forEach(component => {
                    if (window.OTA.Components?.[component]) {
                        logTest(`✅ 组件已加载: ${component}`, 'success');
                    } else {
                        logTest(`❌ 组件未找到: ${component}`, 'error');
                    }
                });
                
                // 检查页面控制器
                if (window.OTA.Pages?.MultiOrderPageV2) {
                    logTest('✅ 多订单页面控制器V2已加载', 'success');
                } else {
                    logTest('❌ 多订单页面控制器V2未找到', 'error');
                }
                
                logTest('🎉 初始化测试完成', 'success');
                
            } catch (error) {
                logTest(`❌ 初始化测试失败: ${error.message}`, 'error');
            }
        }

        // 测试样本订单
        async function testWithSampleOrders() {
            logTest('📋 开始测试样本订单...', 'info');
            
            try {
                // 等待脚本加载完成
                await waitForScriptsLoaded();
                
                // 创建样本订单数据
                const sampleOrders = [
                    {
                        customer_name: '张三',
                        customer_phone: '13800138001',
                        pickup_location: '北京市朝阳区国贸',
                        dropoff_location: '北京市海淀区中关村',
                        pickup_time: '14:30',
                        total_price: 45.5,
                        notes: '请准时到达'
                    },
                    {
                        customer_name: '李四',
                        customer_phone: '13800138002',
                        pickup_location: '上海市浦东新区陆家嘴',
                        dropoff_location: '上海市徐汇区徐家汇',
                        pickup_time: '16:00',
                        total_price: 38.0,
                        notes: '有大件行李'
                    },
                    {
                        customer_name: '王五',
                        customer_phone: '13800138003',
                        pickup_location: '广州市天河区珠江新城',
                        dropoff_location: '广州市越秀区北京路',
                        pickup_time: '18:15',
                        total_price: 52.0,
                        notes: ''
                    }
                ];
                
                logTest(`📋 创建了 ${sampleOrders.length} 个样本订单`, 'info');
                
                // 获取页面控制器
                const pageController = window.OTA.Pages?.multiOrderPageV2;
                if (!pageController) {
                    logTest('❌ 页面控制器未找到', 'error');
                    return;
                }
                
                // 显示多订单页面
                await pageController.show({
                    orders: sampleOrders,
                    originalText: '测试订单数据',
                    detectionMethod: 'manual',
                    confidence: 1.0
                });
                
                logTest('✅ 多订单页面已显示', 'success');
                logTest('🎉 样本订单测试完成', 'success');
                
            } catch (error) {
                logTest(`❌ 样本订单测试失败: ${error.message}`, 'error');
            }
        }

        // 测试服务
        async function testServices() {
            logTest('🔧 开始测试服务...', 'info');
            
            try {
                await waitForScriptsLoaded();
                
                // 测试状态管理器
                const stateManager = window.OTA.Services?.stateManager;
                if (stateManager) {
                    logTest('✅ 状态管理器可用', 'success');
                    
                    // 测试基本功能
                    const stats = stateManager.getStats();
                    logTest(`📊 状态管理器统计: ${JSON.stringify(stats)}`, 'info');
                } else {
                    logTest('❌ 状态管理器不可用', 'error');
                }
                
                // 测试其他服务
                const services = ['orderDetector', 'orderProcessor', 'batchManager', 'apiClient'];
                services.forEach(serviceName => {
                    const service = window.OTA.Services?.[serviceName];
                    if (service) {
                        logTest(`✅ ${serviceName} 服务可用`, 'success');
                    } else {
                        logTest(`❌ ${serviceName} 服务不可用`, 'error');
                    }
                });
                
                logTest('🎉 服务测试完成', 'success');
                
            } catch (error) {
                logTest(`❌ 服务测试失败: ${error.message}`, 'error');
            }
        }

        // 测试组件
        async function testComponents() {
            logTest('🎨 开始测试组件...', 'info');
            
            try {
                await waitForScriptsLoaded();
                
                // 测试组件注册中心
                const registry = window.OTA.Components?.registry;
                if (registry) {
                    logTest('✅ 组件注册中心可用', 'success');
                    
                    const stats = registry.getStats();
                    logTest(`📊 组件注册统计: ${JSON.stringify(stats)}`, 'info');
                } else {
                    logTest('❌ 组件注册中心不可用', 'error');
                }
                
                // 测试基础组件
                const BaseComponent = window.OTA.Components?.BaseComponent;
                if (BaseComponent) {
                    logTest('✅ 基础组件类可用', 'success');
                } else {
                    logTest('❌ 基础组件类不可用', 'error');
                }
                
                logTest('🎉 组件测试完成', 'success');
                
            } catch (error) {
                logTest(`❌ 组件测试失败: ${error.message}`, 'error');
            }
        }

        // 等待脚本加载完成
        function waitForScriptsLoaded() {
            return new Promise((resolve) => {
                if (window.OTA && window.OTA.Services && window.OTA.Components && window.OTA.Pages) {
                    resolve();
                } else {
                    setTimeout(() => {
                        waitForScriptsLoaded().then(resolve);
                    }, 100);
                }
            });
        }

        // 页面加载完成后自动运行初始化测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                logTest('🌟 页面加载完成，开始自动测试...', 'info');
                testInitialization();
            }, 1000);
        });
    </script>
</body>
</html>
