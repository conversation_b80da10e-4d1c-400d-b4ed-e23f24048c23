/**
 * ============================================================================
 * 🚀 核心业务流程 - UI管理器 (待重构为母子两层架构)
 * ============================================================================
 *
 * @fileoverview UI管理器 - 待重构的大文件 (980行)
 * @description 包含UI管理的完整实现，计划拆分为母子两层架构
 *
 * @businessFlow UI管理在核心业务流程中的作用
 * 在核心业务流程中的位置：
 * 输入内容 → 渠道检测 → 提示词组合 → Gemini API → 结果处理
 *     ↓
 * B1. 单订单 → 【当前文件职责】映射到单订单表单
 * B2. 多订单 → 【当前文件职责】触发多订单模式 → 映射到多订单表单
 *     ↓
 * 发送GoMyHire API → 保存到本地历史订单
 *
 * @architecture 待重构 - 当前为单体结构
 * 重构计划：
 * - 拆分为1个母层控制器 + 3个子层实现
 * - 母层：controllers/ui-management-controller.js (UI管理控制)
 * - 子层：ui/dom-manager.js (DOM操作)
 * - 子层：ui/event-manager.js (事件处理)
 * - 子层：ui/form-manager.js (表单管理)
 *
 * @dependencies 当前依赖关系（复杂，待简化）
 * 上游依赖：
 * - BusinessFlowController - 业务流程控制器
 * - OrderManagementController - 订单管理控制器
 * 下游被依赖：
 * - 各种UI组件和表单元素
 * - 事件监听器和用户交互
 *
 * @localProcessing 本地处理职责（当前混合，待分离）
 * - 🟢 DOM元素管理和缓存
 * - 🟢 用户界面状态管理
 * - 🟢 表单数据映射和验证
 * - 🟢 模态框和弹窗管理
 * - 🟢 事件绑定和用户交互
 *
 * @remoteProcessing 远程处理职责
 * - ❌ 无（纯UI管理，不调用远程API）
 *
 * @compatibility 兼容性保证
 * - 保持现有window.OTA.uiManager接口
 * - 保持现有DOM操作方法
 * - 保持现有事件处理机制
 *
 * @refactoringConstraints 重构约束
 * - 文件过大（980行），需要拆分
 * - 职责混乱（DOM+事件+表单），需要清晰分离
 * - 保持UI功能完整性
 * - 严格遵循母子两层架构原则
 *
 * @refactoringPlan 重构计划
 * 阶段1：创建母层UI控制器，保持现有接口
 * 阶段2：逐步迁移DOM管理到子层
 * 阶段3：迁移事件处理到子层
 * 阶段4：迁移表单管理到子层
 * 阶段5：测试验证，确保兼容性
 *
 * <AUTHOR>
 * @version 1.0.0 (待升级到2.0.0)
 * @since 2024-XX-XX
 * @lastModified 2025-08-09
 * @refactoringStatus 计划中
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器

    /**
     * 重构后的UI管理器类
     * 作为各个管理器模块的协调器
     */
    class UIManager {
        constructor() {
            this.isInitialized = false;
            this.managers = {}; // 存储所有子管理器
            this.elements = {}; // 缓存DOM元素
            this.currentModal = null; // 当前活动的模态框
            this.gridResizer = null; // 网格缩放器实例
        }

        /**
         * 初始化UI管理器
         * @description 调整了初始化顺序，确保在执行任何UI逻辑前先缓存DOM元素。
         */
        init() {
            getLogger().log('UIManager 开始初始化...', 'info');

            // 步骤 1: 优先缓存所有DOM元素引用
            this.cacheElements();
            getLogger().log('DOM元素已缓存', 'info');

            // 步骤 2: 根据认证状态显示正确的面板
            const isLoggedIn = getAppState().get('auth.isLoggedIn');
            if (isLoggedIn) {
                this.showWorkspace();
            } else {
                this.showLogin();
            }
            
            // **新增**: 确保系统数据被正确初始化
            this.initializeSystemData();
            
            // 步骤 3: 初始化所有子管理器和事件监听器
            this.initializeManagers();
            this.bindEvents();
            this.setupStateListeners();
            
            // 语言选择器已简化为原生select，无需额外初始化
            // Grid resizer temporarily disabled for three-column layout
            // this.initializeGridResizer();
            
            // 步骤 4: 确保关键功能正确初始化
            this.ensureImageUploadManager();
            
            this.isInitialized = true;
            getLogger().log('UI管理器初始化完成', 'success');
        }

        /**
         * 显示主工作区，并隐藏登录面板
         * @description 直接修改style属性，确保最高优先级
         */
        showWorkspace() {
            getLogger().log('显示主工作区', 'info');
            if (this.elements.workspace) {
                this.elements.workspace.classList.remove('hidden'); // 移除隐藏类，避免!important覆盖
                this.elements.workspace.style.display = 'block'; // 显示工作区
                this.elements.workspace.setAttribute('aria-hidden', 'false'); // 无障碍属性同步
            }
            if (this.elements.loginPanel) {
                this.elements.loginPanel.style.display = 'none'; // 隐藏登录面板
                this.elements.loginPanel.setAttribute('aria-hidden', 'true');
            }
        }

        /**
         * 显示登录面板，并隐藏主工作区
         * @description 直接修改style属性，确保最高优先级
         */
        showLogin() {
            getLogger().log('显示登录面板', 'info');
            if (this.elements.loginPanel) {
                this.elements.loginPanel.style.display = 'block'; // 显示登录面板
                this.elements.loginPanel.setAttribute('aria-hidden', 'false');
            }
            if (this.elements.workspace) {
                this.elements.workspace.style.display = 'none'; // 隐藏工作区
                this.elements.workspace.classList.add('hidden'); // 添加隐藏类，确保样式一致
                this.elements.workspace.setAttribute('aria-hidden', 'true');
            }
        }

        /**
         * 缓存所有需要操作的DOM元素
         */
        cacheElements() {
            this.elements = {
                // 主要容器
                loginPanel: document.getElementById('loginPanel'),
                workspace: document.getElementById('workspace'),
                
                // 登录表单
                loginForm: document.getElementById('loginForm'),
                emailInput: document.getElementById('email'),
                passwordInput: document.getElementById('password'),
                rememberMe: document.getElementById('rememberMe'),
                loginBtn: document.getElementById('loginBtn'),
                logoutBtn: document.getElementById('logoutBtn'),
                historyBtn: document.getElementById('historyBtn'),
                clearSavedBtn: document.getElementById('clearSavedBtn'),
                
                // 订单输入
                orderInput: document.getElementById('orderInput'),
                parseBtn: document.getElementById('parseBtn'),
                
                // 表单字段
                orderForm: document.getElementById('orderForm'),
                customerName: document.getElementById('customerName'),
                customerContact: document.getElementById('customerContact'),
                customerEmail: document.getElementById('customerEmail'),
                pickup: document.getElementById('pickup'),
                dropoff: document.getElementById('dropoff'),
                pickupDate: document.getElementById('pickupDate'),
                pickupTime: document.getElementById('pickupTime'),
                passengerCount: document.getElementById('passengerCount'),
                luggageCount: document.getElementById('luggageCount'),
                flightInfo: document.getElementById('flightInfo'),
                otaPrice: document.getElementById('otaPrice'),
                driverFee: document.getElementById('driverFee'),
                currency: document.getElementById('currency'),
                otaReferenceNumber: document.getElementById('otaReferenceNumber'),
                extraRequirement: document.getElementById('extraRequirement'),
                remark: document.getElementById('remark'),
                
                // 下拉选择
                subCategoryId: document.getElementById('subCategoryId'),
                carTypeId: document.getElementById('carTypeId'),
                inchargeByBackendUserId: document.getElementById('inchargeByBackendUserId'),
                drivingRegionId: document.getElementById('drivingRegionId'),
                languagesCheckboxes: document.querySelector('.language-checkboxes'), // 语言复选框容器
                otaChannel: document.getElementById('ota'), // **修复**: 修正元素ID匹配
                otaChannelCustom: document.getElementById('otaChannelCustom'),
                
                // 操作按钮
                createBtn: document.getElementById('createOrder'),
                resetBtn: document.getElementById('resetBtn'),
                returnToMultiOrderBtn: document.getElementById('returnToMultiOrder'),
                
                // 状态显示
                userInfo: document.getElementById('userInfo'),
                currentUser: document.getElementById('currentUser'),
                persistentEmailContainer: document.getElementById('persistentEmailContainer'),
                persistentEmail: document.getElementById('persistentEmail'),
                saveEmailBtn: document.getElementById('saveEmailBtn'),
                geminiStatus: document.getElementById('geminiStatus'),
                dataStatus: document.getElementById('dataStatus'),
                connectionStatus: document.getElementById('connectionStatus'),
                lastUpdate: document.getElementById('lastUpdate'),
                
                // 模态框
                modal: document.getElementById('modal'),
                modalTitle: document.getElementById('modalTitle'),
                modalContent: document.getElementById('modalBody'),
                modalCancel: document.getElementById('modalCancel'),
                modalClose: document.getElementById('modalClose'),
                
                // 其他控件
                themeToggle: document.getElementById('themeToggle'),
                languageSelect: document.getElementById('languageSelect'),
                clearLogsBtn: document.getElementById('clearLogsBtn'),
                exportLogsBtn: document.getElementById('exportLogsBtn'),
                debugMode: document.getElementById('debugMode'),
                
                // **新增**: 图片上传相关元素
                imageUploadButton: document.getElementById('imageUploadButton'),
                imageFileInput: document.getElementById('imageFileInput'),
                imageUploadStatus: document.getElementById('imageUploadStatus'),
                imagePreviewContainer: document.getElementById('imagePreviewContainer'),
                
                // **新增**: 历史订单相关元素
                historyPanel: document.getElementById('historyPanel'),
                historyListContainer: document.getElementById('historyListContainer')
            };

            getLogger().log('DOM元素缓存完成', 'info');
        }

        /**
         * 初始化各个管理器模块
         */
        initializeManagers() {
            // 初始化表单管理器
            if (window.OTA.managers.FormManager) {
                this.managers.form = new window.OTA.managers.FormManager(this.elements);
                this.managers.form.init();

                // 🚀 浏览器兼容性增强：统一注册策略，确保所有浏览器中都能正确注册
                this.registerFormManagerWithRetry();
            }

            // 价格管理器已移除 - 性能优化

            // 初始化状态管理器
            if (window.OTA.managers.StateManager) {
                this.managers.state = new window.OTA.managers.StateManager(this.elements);
                this.managers.state.init();
            }

            // 初始化事件管理器
            if (window.OTA.managers.EventManager) {
                this.managers.event = new window.OTA.managers.EventManager(this.elements, this);
                this.managers.event.init();
            }

            // 初始化实时分析管理器
            if (window.OTA.managers.RealtimeAnalysisManager) {
                this.managers.realtimeAnalysis = new window.OTA.managers.RealtimeAnalysisManager(this.elements, this);
                this.managers.realtimeAnalysis.init();
            }

            getLogger().log('管理器模块初始化完成', 'success');
        }

        /**
         * 绑定事件监听器（委托给事件管理器）
         */
        bindEvents() {
            // 事件绑定已委托给EventManager
            // 这里只保留一些必要的全局事件
            
            // 窗口大小变化事件
            window.addEventListener('resize', () => {
                this.handleWindowResize();
            });

            // 页面可见性变化事件
            document.addEventListener('visibilitychange', () => {
                this.handleVisibilityChange();
            });
        }

        /**
         * 设置状态监听器（委托给状态管理器）
         */
        setupStateListeners() {
            const appState = getAppState();

            // 监听认证状态变化，自动切换UI
            appState.on('auth.isLoggedIn', (isLoggedIn) => {
                getLogger().log(`认证状态变更为: ${isLoggedIn}，更新UI`, 'info');
                if (isLoggedIn) {
                    this.showWorkspace();
                } else {
                    this.showLogin();
                }
            });

            // 监听主题变化
            appState.on('config.theme', (theme) => {
                document.body.className = `theme-${theme}`;
                getLogger().log(`主题已切换为: ${theme}`, 'info');
            });
            
            // 监听调试模式变化
            appState.on('config.debugMode', (isDebug) => {
                document.body.classList.toggle('debug-mode', isDebug);
                getLogger().log(`调试模式已${isDebug ? '开启' : '关闭'}`, 'info');
            });
        }



        /**
         * 初始化系统数据
         * 确保系统数据（包括语言列表）被正确加载到AppState中
         */
        initializeSystemData() {
            try {
                // 检查系统数据是否已加载
                let systemData = getAppState().get('systemData') || {};
                
                // 如果系统数据为空或语言数据为空，从API服务加载静态数据
                if (!systemData.languages || systemData.languages.length === 0) {
                    const apiService = getApiService();
                    if (apiService && apiService.staticData) {
                        systemData = apiService.staticData;
                        getAppState().setSystemData(systemData);
                        getLogger().log('系统数据已初始化并加载', 'success', {
                            languagesCount: systemData.languages?.length || 0,
                            backendUsersCount: systemData.backendUsers?.length || 0,
                            carTypesCount: systemData.carTypes?.length || 0,
                            drivingRegionsCount: systemData.drivingRegions?.length || 0
                        });
                    } else {
                        getLogger().log('API服务不可用，无法加载系统数据', 'error');
                    }
                } else {
                    getLogger().log('系统数据已存在，无需重新初始化', 'info', {
                        languagesCount: systemData.languages?.length || 0
                    });
                }
            } catch (error) {
                getLogger().logError('初始化系统数据时发生错误', {
                    error: error.message,
                    stack: error.stack
                });
            }
        }

        /**
         * 初始化田字格拖拽功能
         */
        initializeGridResizer() {
            // 田字格拖拽功能已移至独立模块
            // 这里可以初始化相关功能
            getLogger().log('田字格拖拽功能初始化完成', 'info');
        }

        /**
         * 浏览器兼容性增强：重试机制注册FormManager
         * 解决不同浏览器中时序和DOM就绪状态检测差异
         */
        registerFormManagerWithRetry() {
            const attemptRegistration = (attempt = 1, maxAttempts = 5) => {
                try {
                    // 检查必要条件
                    if (!window.OTA?.container || typeof window.OTA.container.register !== 'function') {
                        if (attempt < maxAttempts) {
                            console.warn(`[UIManager] 依赖容器未就绪，第${attempt}次尝试注册FormManager，将重试...`);
                            setTimeout(() => attemptRegistration(attempt + 1, maxAttempts), 200 * attempt);
                            return;
                        } else {
                            console.error('[UIManager] 依赖容器始终未就绪，FormManager注册失败');
                            return;
                        }
                    }

                    // 浏览器兼容性：更可靠的重复注册检查
                    const hasFormManager = window.OTA.container.instances?.has?.('formManager') || 
                                          window.OTA.container.has?.('formManager');
                    const hasFormManagerClass = window.OTA.container.instances?.has?.('formManagerClass') || 
                                               window.OTA.container.has?.('formManagerClass');

                    if (!hasFormManager || !hasFormManagerClass) {
                        // 注册FormManager类（如果不存在）
                        if (!hasFormManagerClass && window.OTA.managers?.FormManager) {
                            window.OTA.container.register('formManagerClass', () => window.OTA.managers.FormManager, {
                                singleton: true
                            });
                        }

                        // 注册FormManager实例（如果不存在）
                        if (!hasFormManager && this.managers?.form) {
                            window.OTA.container.register('formManager', () => this.managers.form, {
                                singleton: true
                            });
                        }

                        console.log('[UIManager] ✅ FormManager类和实例已统一注册（浏览器兼容性增强）');
                    } else {
                        console.log('[UIManager] FormManager已存在，跳过注册');
                    }
                } catch (error) {
                    if (attempt < maxAttempts) {
                        console.warn(`[UIManager] FormManager注册失败（第${attempt}次尝试），将重试:`, error.message);
                        setTimeout(() => attemptRegistration(attempt + 1, maxAttempts), 200 * attempt);
                    } else {
                        console.error('[UIManager] FormManager注册最终失败:', error.message);
                    }
                }
            };

            // 立即尝试注册，如果失败则启动重试机制
            attemptRegistration();
        }

        /**
         * 确保图片上传管理器正确初始化
         */
        ensureImageUploadManager() {
            try {
                // 检查图片上传管理器是否存在
                let imageManager = null;
                
                if (window.getImageUploadManager) {
                    imageManager = window.getImageUploadManager();
                } else if (window.ImageUploadManager) {
                    imageManager = new window.ImageUploadManager();
                }

                if (imageManager) {
                    // 确保绑定到正确的按钮
                    const imageUploadButton = document.getElementById('imageUploadButton');
                    const imageFileInput = document.getElementById('imageFileInput');
                    
                    if (imageUploadButton && imageFileInput) {
                        // 移除可能存在的旧监听器并添加新的
                        imageUploadButton.removeEventListener('click', imageManager.triggerFileSelect);
                        imageUploadButton.addEventListener('click', () => {
                            imageFileInput.click();
                        });
                        
                        getLogger().log('图片上传按钮事件已重新绑定', 'success');
                    }
                } else {
                    getLogger().log('图片上传管理器不可用', 'warning');
                }
            } catch (error) {
                getLogger().log(`图片上传管理器初始化失败: ${error.message}`, 'error');
            }
        }

        /**
         * 处理窗口大小变化
         */
        handleWindowResize() {
            // 响应式布局调整
            this.updateResponsiveLayout();
        }

        /**
         * 处理页面可见性变化
         */
        handleVisibilityChange() {
            if (document.hidden) {
                // 页面隐藏时暂停实时分析
                if (this.managers.realtimeAnalysis) {
                    this.managers.realtimeAnalysis.setRealtimeAnalysisEnabled(false);
                }
            } else {
                // 页面显示时恢复实时分析
                if (this.managers.realtimeAnalysis) {
                    this.managers.realtimeAnalysis.setRealtimeAnalysisEnabled(true);
                }
            }
        }

        /**
         * 更新响应式布局
         */
        updateResponsiveLayout() {
            // 移除移动端布局类的添加，保持多列布局
            const workspace = this.elements.workspace;

            if (workspace) {
                // 确保移除任何可能的单列布局类
                workspace.classList.remove('mobile-layout', 'single-column');
            }
        }

        // ==================== 公共API方法 ====================
        // 以下方法提供给其他模块调用的统一接口

        /**
         * 填充表单选项（委托给表单管理器）
         */
        populateFormOptions() {
            if (this.managers.form) {
                this.managers.form.populateFormOptions();
            }
        }

        /**
         * 从数据填充表单（委托给表单管理器）
         * @param {object} data - 表单数据
         */
        fillFormFromData(data) {
            if (this.managers.form) {
                this.managers.form.fillFormFromData(data);
            }
        }

        /**
         * 收集表单数据（委托给表单管理器）
         * @returns {object} 表单数据
         */
        collectFormData() {
            if (this.managers.form) {
                return this.managers.form.collectFormData();
            }
            return {};
        }

        /**
         * 处理价格转换（委托给价格管理器）
         * @param {object} orderData - 订单数据
         */
        processPriceConversion(orderData) {
            // 价格管理器已移除，无需处理
        }

        /**
         * 更新价格转换显示（委托给价格管理器）
         */
        updatePriceConversion() {
            // 价格管理器已移除，无需处理
        }

        /**
         * 设置按钮加载状态（委托给状态管理器）
         * @param {HTMLButtonElement} button - 按钮元素
         * @param {boolean} loading - 是否加载中
         */
        setButtonLoading(button, loading) {
            if (this.managers.state) {
                this.managers.state.setButtonLoading(button, loading);
            }
        }

        /**
         * 显示提示消息（委托给状态管理器）
         * @param {string} message - 消息内容
         * @param {string} type - 消息类型
         * @param {number} duration - 显示时长
         */
        showAlert(message, type = 'info', duration = 5000) {
            if (this.managers.state) {
                this.managers.state.showAlert(message, type, duration);
            }
        }

        /**
         * 显示快速提示（委托给状态管理器）
         * @param {string} message - 提示消息
         * @param {string} type - 提示类型
         */
        showQuickToast(message, type = 'info') {
            if (this.managers.state) {
                this.managers.state.showQuickToast(message, type);
            }
        }

        /**
         * 显示模态框
         * @param {string} title - 标题
         * @param {string} content - 内容
         * @param {Object} options - 选项
         */
        showModal(title, content, options = {}) {
            this.currentModal = this.elements.modal;

            if (this.elements.modalTitle) {
                this.elements.modalTitle.textContent = title;
            }
            if (this.elements.modalContent) {
                this.elements.modalContent.innerHTML = content;
            }
            if (this.elements.modal) {
                // 移除hidden类并设置display为flex
                this.elements.modal.classList.remove('hidden');
                this.elements.modal.style.display = 'flex';
            }
        }

        /**
         * 隐藏模态框
         */
        hideModal() {
            this.currentModal = null;
            if (this.elements.modal) {
                this.elements.modal.style.display = 'none';
                this.elements.modal.classList.add('hidden');
            }
        }

        /**
         * 显示确认对话框
         * @param {string} title - 标题
         * @param {string} message - 消息
         * @param {Function} onConfirm - 确认回调
         * @param {Function} onCancel - 取消回调
         */
        showConfirm(title, message, onConfirm, onCancel) {
            const content = `<p>${message}</p>`;

            this.showModal(title, content);

            // 使用模态框footer的按钮
            const modalConfirmBtn = document.getElementById('modalConfirm');
            const modalCancelBtn = document.getElementById('modalCancel');
            
            if (modalConfirmBtn) {
                // 移除之前的事件监听器
                const newConfirmBtn = modalConfirmBtn.cloneNode(true);
                modalConfirmBtn.parentNode.replaceChild(newConfirmBtn, modalConfirmBtn);
                
                newConfirmBtn.addEventListener('click', () => {
                    this.hideModal();
                    if (onConfirm) onConfirm();
                });
            }

            if (modalCancelBtn) {
                // 移除之前的事件监听器
                const newCancelBtn = modalCancelBtn.cloneNode(true);
                modalCancelBtn.parentNode.replaceChild(newCancelBtn, modalCancelBtn);
                
                newCancelBtn.addEventListener('click', () => {
                    this.hideModal();
                    if (onCancel) onCancel();
                });
            }
        }

        /**
         * 显示详细的错误信息弹窗
         * @param {Error} error - 错误对象，包含message, validationErrors, originalError等
         * @param {Object} options - 显示选项
         */
        showErrorModal(error, options = {}) {
            // 根据错误类型调整标题
            let title = options.title || 'Order Creation Failed';
            if (error.isDuplicateOrder) {
                title = '⚠️ Duplicate Order Detected';
            }

            // 构建错误信息内容
            let content = '<div class="error-modal-content">';

            // 重复订单的特殊提示
            if (error.isDuplicateOrder) {
                content += `<div class="error-duplicate-notice" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border: 1px solid #e74c3c; padding: 15px; margin-bottom: 15px; border-radius: 8px; color: white; box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);">
                    <strong>🚫 重复订单警告:</strong><br>
                    该OTA参考号已在系统中存在。每个订单必须具有唯一的OTA参考号。
                </div>`;
            }

            // 主要错误消息
            const mainMessage = this.getMainErrorMessage(error);
            content += `<div class="error-main-message">
                <h4>Main Issue:</h4>
                <p>${mainMessage}</p>
            </div>`;

            // API详细错误（如果有validation_error）
            if (error.validationErrors && Object.keys(error.validationErrors).length > 0) {
                content += '<div class="error-validation-details">';
                content += '<h4>Detailed Errors:</h4>';
                content += '<ul class="error-list">';

                Object.keys(error.validationErrors).forEach(field => {
                    const fieldErrors = error.validationErrors[field];
                    const errorMessages = Array.isArray(fieldErrors) ? fieldErrors : [fieldErrors];

                    errorMessages.forEach(errorMsg => {
                        content += `<li><strong>${field}:</strong> ${errorMsg}</li>`;
                    });
                });

                content += '</ul></div>';
            }

            // API响应信息（如果有完整的API响应）
            if (error.apiResponse && error.apiResponse.message && 
                error.apiResponse.message !== error.message) {
                content += `<div class="error-api-response">
                    <h4>API Response:</h4>
                    <p style="font-family: monospace; background: #ffebee69; color: #c62828; padding: 12px; border-radius: 6px; border-left: 4px solid #f44336; box-shadow: 0 2px 4px rgba(244, 67, 54, 0.1);">${error.apiResponse.message}</p>
                </div>`;
            }

            // OTA参考号信息（对于重复订单）
            if (error.isDuplicateOrder && error.requestData?.ota_reference) {
                content += `<div class="error-ota-reference">
                    <h4>OTA Reference:</h4>
                    <p><strong>${error.requestData.ota_reference}</strong></p>
                </div>`;
            }

            // 错误代码（如果有）
            const errorCode = this.getErrorCode(error);
            if (errorCode) {
                content += `<div class="error-code">
                    <h4>Error Code:</h4>
                    <p>${errorCode}</p>
                </div>`;
            }

            // 建议操作
            const suggestions = this.getErrorSuggestions(error);
            if (suggestions) {
                content += `<div class="error-suggestions">
                    <h4>Suggested Actions:</h4>
                    <p>${suggestions}</p>
                </div>`;
            }

            // 关闭按钮
            content += `<div class="modal-actions">
                <button id="errorModalCloseBtn" class="btn btn-primary">Close</button>
            </div>`;

            content += '</div>';

            // 显示模态框
            this.showModal(title, content);

            // 绑定关闭按钮事件（防止重复绑定）
            const closeBtn = document.getElementById('errorModalCloseBtn');
            if (closeBtn) {
                // 使用克隆节点替换的方式移除重复的事件监听器
                const newCloseBtn = closeBtn.cloneNode(true);
                closeBtn.parentNode.replaceChild(newCloseBtn, closeBtn);
                
                newCloseBtn.addEventListener('click', () => {
                    this.hideModal();
                });
            }

            // 记录错误显示日志
            getLogger().log('显示错误弹窗', 'info', {
                errorType: error.constructor.name,
                hasValidationErrors: !!error.validationErrors,
                errorMessage: error.message,
                timestamp: new Date().toISOString()
            });
        }

        /**
         * 获取主要错误消息
         * @param {Error} error - 错误对象
         * @returns {string} 用户友好的错误消息
         */
        getMainErrorMessage(error) {
            // 重复订单错误优先显示
            if (error.isDuplicateOrder) {
                return error.message || 'OTA参考号已存在，无法创建重复订单';
            }
            
            // 优先级：友好错误消息 > 验证错误总结 > 原始错误消息
            if (error.message && !error.message.includes('Failed to fetch') &&
                !error.message.includes('NetworkError') &&
                error.message !== 'Order creation failed') {
                return error.message;
            }

            if (error.validationErrors && Object.keys(error.validationErrors).length > 0) {
                const fieldCount = Object.keys(error.validationErrors).length;
                return `Data validation failed for ${fieldCount} field${fieldCount > 1 ? 's' : ''}`;
            }

            if (error.originalError && error.originalError.message) {
                return error.originalError.message;
            }

            return 'An unexpected error occurred during order creation';
        }

        /**
         * 获取错误代码
         * @param {Error} error - 错误对象
         * @returns {string|null} 错误代码
         */
        getErrorCode(error) {
            const codes = [];

            // HTTP状态码
            if (error.details && error.details.status) {
                codes.push(`HTTP ${error.details.status}`);
            }

            // API错误代码
            if (error.details && error.details.code) {
                codes.push(`API ${error.details.code}`);
            }

            // 原始错误中的状态码
            if (error.originalError && error.originalError.status) {
                codes.push(`Status ${error.originalError.status}`);
            }

            return codes.length > 0 ? codes.join(', ') : null;
        }

        /**
         * 获取错误建议操作
         * @param {Error} error - 错误对象
         * @returns {string} 建议操作
         */
        getErrorSuggestions(error) {
            // 重复订单错误的特殊建议
            if (error.isDuplicateOrder) {
                const otaRef = error.requestData?.ota_reference || 'unknown';
                return `OTA参考号 "${otaRef}" 已在系统中存在。请检查：
                    <br>• 更改OTA参考号为唯一值
                    <br>• 确认是否需要修改现有订单而非创建新订单
                    <br>• 联系管理员查看重复订单详情`;
            }
            
            if (error.validationErrors && Object.keys(error.validationErrors).length > 0) {
                return 'Please check and correct the highlighted fields above, then resubmit the order.';
            }

            if (error.message && error.message.includes('network')) {
                return 'Please check your internet connection and try again.';
            }

            if (error.message && error.message.includes('timeout')) {
                return 'The request timed out. Please try again in a few moments.';
            }

            if (error.message && error.message.includes('server')) {
                return 'Server error occurred. Please try again later or contact support.';
            }

            return 'Please review the order details and try again. If the problem persists, contact support.';
        }

        /**
         * 显示API密钥提示
         */
        showApiKeyPrompt() {
            const content = `
                <p>请先配置Gemini API密钥才能使用AI分析功能。</p>
                <div class="api-key-input">
                    <input type="password" id="apiKeyInput" placeholder="输入Gemini API密钥" />
                    <button id="saveApiKeyBtn" class="btn btn-primary">保存</button>
                </div>
            `;

            this.showModal('配置API密钥', content);

            const saveBtn = document.getElementById('saveApiKeyBtn');
            const input = document.getElementById('apiKeyInput');

            if (saveBtn && input) {
                saveBtn.addEventListener('click', () => {
                    const apiKey = input.value.trim();
                    if (apiKey) {
                        getGeminiService().setApiKey(apiKey);
                        this.hideModal();
                        this.showAlert('API密钥已保存', 'success');
                    }
                });
            }
        }

        /**
         * 显示简洁的成功提示
         * @param {string} orderId - 订单ID
         */
        showSimpleSuccessToast(orderId) {
            this.showQuickToast(`✅ 订单创建成功 (ID: ${orderId})`, 'success');
        }

        /**
         * 显示预览模态框
         */
        showPreviewModal() {
            const previewSection = document.getElementById('previewSection');
            if (previewSection) {
                previewSection.style.display = 'flex';
                this.updatePreviewContent();
            }
        }

        /**
         * 隐藏预览模态框
         */
        hidePreviewModal() {
            const previewSection = document.getElementById('previewSection');
            if (previewSection) {
                previewSection.style.display = 'none';
            }
        }

        /**
         * 更新预览内容
         */
        updatePreviewContent() {
            const currentOrder = getAppState().get('currentOrder');
            if (!currentOrder) return;

            const previewContent = document.getElementById('previewContent');
            if (!previewContent) return;

            const data = currentOrder.parsedData || currentOrder;
            const confidence = currentOrder.confidence || 0;

            let html = `
                <div class="preview-header">
                    <h3>📋 订单预览</h3>
                    <div class="confidence-badge ${this.getConfidenceClass(confidence)}">
                        置信度: ${confidence}%
                    </div>
                </div>
                <div class="preview-sections">
            `;

            // 客户信息
            html += this.generatePreviewSection('👤 客户信息', {
                '姓名': data.customer_name,
                '联系电话': data.customer_contact,
                '邮箱': data.customer_email
            });

            // 行程信息
            html += this.generatePreviewSection('🚗 行程信息', {
                '上车地点': data.pickup_location,
                '目的地': data.dropoff_location,
                '日期': data.pickup_date,
                '时间': data.pickup_time,
                '乘客人数': data.passenger_number,
                '行李件数': data.luggage_count,
                '航班号': data.flight_number
            });

            // 订单信息
            html += this.generatePreviewSection('📄 订单信息', {
                'OTA价格': data.ota_price ? `${data.ota_price} ${data.currency || ''}` : '',
                '参考号': data.ota_reference_number,
                '额外要求': data.extra_requirement,
                '备注': data.remark
            });

            html += `
                </div>
                <div class="preview-actions">
                    <button id="applyPreviewBtn" class="btn btn-primary">应用到表单</button>
                    <button id="closePreviewBtn" class="btn btn-secondary">关闭</button>
                </div>
            `;

            previewContent.innerHTML = html;

            // 绑定按钮事件
            const applyBtn = document.getElementById('applyPreviewBtn');
            const closeBtn = document.getElementById('closePreviewBtn');

            if (applyBtn) {
                applyBtn.addEventListener('click', () => {
                    this.fillFormFromData(data);
                    this.hidePreviewModal();
                });
            }

            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    this.hidePreviewModal();
                });
            }
        }

        /**
         * 生成预览区块
         * @param {string} title - 区块标题
         * @param {object} data - 数据对象
         * @returns {string} HTML字符串
         */
        generatePreviewSection(title, data) {
            let html = `<div class="preview-section"><h4>${title}</h4><div class="preview-fields">`;

            for (const [label, value] of Object.entries(data)) {
                if (value !== null && value !== undefined && value !== '') {
                    html += `<div class="preview-field"><span class="field-label">${label}:</span><span class="field-value">${value}</span></div>`;
                }
            }

            html += '</div></div>';
            return html;
        }

        /**
         * 获取置信度样式类
         * @param {number} confidence - 置信度
         * @returns {string} CSS类名
         */
        getConfidenceClass(confidence) {
            if (confidence >= 80) return 'confidence-high';
            if (confidence >= 60) return 'confidence-medium';
            return 'confidence-low';
        }

        /**
         * 获取管理器实例
         * @param {string} managerName - 管理器名称
         * @returns {object} 管理器实例
         */
        getManager(managerName) {
            return this.managers[managerName];
        }

        /**
         * 检查是否已初始化
         * @returns {boolean} 是否已初始化
         */
        isReady() {
            return this.isInitialized;
        }

        /**
         * 显示加载遮罩
         */
        showLoadingOverlay() {
            const overlay = document.createElement('div');
            overlay.classList.add('loading-overlay');
            document.body.appendChild(overlay);
        }

        /**
         * 隐藏加载遮罩
         */
        hideLoadingOverlay() {
            const overlay = document.querySelector('.loading-overlay');
            if (overlay) {
                overlay.remove();
            }
        }
    }

    // 导出到全局命名空间
    window.OTA.uiManager = new UIManager();
    window.uiManager = window.OTA.uiManager; // 向后兼容
    
    // 初始化完成后暴露管理器实例到全局（方便调试和访问）
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            if (window.uiManager && window.uiManager.managers) {
                window.eventManager = window.uiManager.managers.event;
                window.formManager = window.uiManager.managers.form;
                window.stateManager = window.uiManager.managers.state;
                // window.priceManager = window.uiManager.managers.price; // 已移除
                window.realtimeAnalysisManager = window.uiManager.managers.realtimeAnalysis;
            }
        }, 100); // 短暂延迟确保初始化完成
    });

})();
